# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from typing import Any

from . import _automatic_function_calling_util
from .langchain_tool import LangchainTool


class ToolboxTool:
  """A class that provides access to toolbox tools.

  Example:
  ```python
  toolbox = ToolboxTool("http://127.0.0.1:5000")
  tool = toolbox.get_tool("tool_name")
  toolset = toolbox.get_toolset("toolset_name")
  ```
  """

  toolbox_client: Any
  """The toolbox client."""

  def __init__(self, url: str):
    from toolbox_langchain import ToolboxClient

    self.toolbox_client = ToolboxClient(url)

  def get_tool(self, tool_name: str) -> LangchainTool:
    tool = self.toolbox_client.load_tool(tool_name)
    return LangchainTool(tool)

  def get_toolset(self, toolset_name: str) -> list[LangchainTool]:
    tools = self.toolbox_client.load_toolset(toolset_name)
    return [LangchainTool(tool) for tool in tools]
